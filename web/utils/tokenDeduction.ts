import { createClient } from '@/lib/supabase';

/**
 * Token costs for different resume types
 */
export const RESUME_TOKEN_COSTS = {
  JOB_SPECIFIC: 25, // AI-generated resume tailored to job posting
  MANUAL: 15,       // Manual resume created from scratch
} as const;

/**
 * Deduct tokens from user's account for resume generation/download
 * @param resumeId - The resume ID to identify the user and resume type
 * @param tokenCost - Number of tokens to deduct
 * @param operation - Description of the operation for logging
 * @returns Promise<boolean> - Success status
 */
export async function deductResumeTokens(
  resumeId: string, 
  tokenCost: number, 
  operation: string
): Promise<boolean> {
  try {
    const supabase = createClient();

    // Get the user ID from the resume
    const { data: resume, error: resumeError } = await supabase
      .from('resumes')
      .select('user_id')
      .eq('id', resumeId)
      .single();

    if (resumeError || !resume) {
      console.error(`Error fetching resume for token deduction (${operation}):`, resumeError);
      return false;
    }

    const userId = resume.user_id;
    
    // Skip token deduction for unauthenticated users
    if (!userId) {
      console.log(`Skipping token deduction for unauthenticated user (${operation})`);
      return true;
    }

    if (tokenCost <= 0) {
      console.log(`No tokens to deduct for ${operation}`);
      return true;
    }

    // Get current token balance
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('tokens')
      .eq('id', userId)
      .single();

    if (profileError || !profile) {
      console.error(`Error fetching user profile for token deduction (${operation}):`, profileError);
      return false;
    }

    const currentTokens = (profile.tokens as number | null) ?? 0;
    const newTokens = Math.max(currentTokens - tokenCost, 0);

    // Update token balance
    const { error: updateError } = await supabase
      .from('profiles')
      .update({ tokens: newTokens })
      .eq('id', userId);

    if (updateError) {
      console.error(`Error updating token balance (${operation}):`, updateError);
      return false;
    }

    console.log(`Successfully deducted ${tokenCost} tokens from user ${userId} for ${operation}. New balance: ${newTokens}`);
    return true;

  } catch (error) {
    console.error(`Unexpected error during token deduction (${operation}):`, error);
    return false;
  }
}

/**
 * Check if a resume is job-specific (AI-generated) or manual
 * Job-specific resumes have jobInput data in their data field
 * Manual resumes don't have jobInput data
 * @param resumeData - The resume data from database
 * @returns 'job-specific' | 'manual'
 */
export function getResumeType(resumeData: any): 'job-specific' | 'manual' {
  // Check if the resume has job input data (indicates AI generation)
  const hasJobInput = resumeData?.data?.jobInput;
  
  return hasJobInput ? 'job-specific' : 'manual';
}

/**
 * Get token cost for a resume based on its type
 * @param resumeType - The type of resume ('job-specific' | 'manual')
 * @returns number - Token cost
 */
export function getResumeTokenCost(resumeType: 'job-specific' | 'manual'): number {
  return resumeType === 'job-specific' 
    ? RESUME_TOKEN_COSTS.JOB_SPECIFIC 
    : RESUME_TOKEN_COSTS.MANUAL;
}

/**
 * Deduct tokens for job-specific resume after successful generation
 * @param resumeId - The resume ID
 * @returns Promise<boolean> - Success status
 */
export async function deductJobSpecificResumeTokens(resumeId: string): Promise<boolean> {
  return deductResumeTokens(
    resumeId, 
    RESUME_TOKEN_COSTS.JOB_SPECIFIC, 
    'job-specific resume generation'
  );
}

/**
 * Deduct tokens for manual resume after successful download
 * @param resumeId - The resume ID
 * @returns Promise<boolean> - Success status
 */
export async function deductManualResumeTokens(resumeId: string): Promise<boolean> {
  return deductResumeTokens(
    resumeId, 
    RESUME_TOKEN_COSTS.MANUAL, 
    'manual resume download'
  );
}
